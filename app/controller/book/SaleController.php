<?php

namespace app\controller\book;

use app\controller\BookController;

class SaleController extends BookController
{
    public function index()
    {
        $sales = $this->book->sales()->with(['user'])->order('id desc')->paginate();

        return view('book/sale')->assign('sales', $sales);
    }

    public function delete($id)
    {
        /** @var \app\model\BookSale $sale */
        $sale = $this->book->sales()->findOrFail($id);

        if (!$sale->canRevoke()) {
            throw new \think\exception\ValidateException('该订单已超过7天，无法撤销');
        }

        $sale->ord->revoke();
    }
}
