<?php

namespace app\model;

use think\Model;

/**
 * Class app\model\SpaceIncome
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $update_time
 * @property int $amount 收入金额(分)
 * @property int $id
 * @property int $source_id
 * @property int $space_id 空间ID
 * @property int $type 类型：增加/减少
 * @property string $info 备注
 * @property string $source_type
 * @property-read \app\model\Space $space
 */
class SpaceEarnings extends Model
{
    const TYPE_INC = 1;
    const TYPE_DEC = 2;

    /**
     * 关联空间
     */
    public function space()
    {
        return $this->belongsTo(Space::class);
    }

    /**
     * 获取金额（元）
     */
    protected function getAmountAttr()
    {
        return $this->amount / 100;
    }

}
