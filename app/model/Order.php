<?php

namespace app\model;

use app\lib\Cloud;
use app\lib\Hashids;
use Exception;
use think\exception\ValidateException;
use think\Model;

/**
 * Class app\model\Order
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $update_time
 * @property \app\lib\Goods $goods
 * @property int $payable_id
 * @property mixed $amount
 * @property mixed $id
 * @property mixed $status
 * @property mixed $subject
 * @property mixed $user_id
 * @property string $payable_type
 * @property-read \app\model\BookSale $payable
 * @property-read \app\model\User $user
 */
class Order extends Model
{
    protected $type = [
        'goods' => 'serialize',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function payable()
    {
        return $this->morphTo();
    }

    public function paid()
    {
        $this->transaction(function () {
            if ($this->status == 0) {
                $model = $this->goods->invoke($this);
                if ($model instanceof Model) {
                    $this->payable()->associate($model);
                }
            }
            $this->save(['status' => 1]);
        });
    }

    public function revoked()
    {
        $this->transaction(function () {
            if ($this->status == 1) {
                $this->goods->revoke($this);
            }
            $this->save(['status' => -1]);
        });
    }

    public function pay()
    {
        $params = [
            'order_no'   => Hashids::encode($this->id, 'order'),
            'subject'    => $this->subject,
            'amount'     => $this->amount,
            'user_id'    => $this->user?->getOpenid(),
            'return_url' => $this->goods->getReturnUrl(),
            'notify_url' => (string) main_url('/webhook/charge/paid'),
        ];

        if ($this->goods->canRevoke()) {
            $params['revoke_url'] = (string) main_url('/webhook/charge/revoked');
        }

        /** @var Cloud $cloud */
        $cloud = app(Cloud::class);

        return $cloud->createCharge($params);
    }

    public function revoke()
    {
        if ($this->status != 1) {
            throw new ValidateException('当前订单状态不允许撤销');
        }

        if (!$this->goods->canRevoke()) {
            throw new ValidateException('该商品的订单不支持撤销');
        }

        $this->transaction(function () {
            $this->revoked();
            /** @var Cloud $cloud */
            $cloud = app(Cloud::class);

            $cloud->revokeCharge([
                'order_no' => Hashids::encode($this->id, 'order'),
            ]);
        });
    }
}
