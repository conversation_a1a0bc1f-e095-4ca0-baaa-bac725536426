{% extends "book/layout.twig" %}
{% block page_title %}
    销售记录
{% endblock %}
{% block page_content %}
    <table class="table table-hover align-middle">
        <thead>
        <tr>
            <th>名称</th>
            <th>购买时间</th>
            <th width='200'>售价</th>
            <th class='text-end'>操作</th>
        </tr>
        </thead>
        <tbody>
        {% for sale in sales %}
            <tr>
                <td>
                    <span class='d-flex align-items-center'>
                        <img class='me-2 rounded-circle' src='{{ sale.user.avatar }}' height='24' width='24' />
                        {{ sale.user.name }}
                    </span>
                </td>
                <td>{{ sale.create_time }}</td>
                <td>
                    ￥{{ format_price(sale.amount) }}
                </td>
                <td class='text-end'>
                    {% if sale.canRevoke() %}
                        <a class='text-danger' data-bs-confirm data-message='确定要撤销该订单吗？' data-method='delete'
                           href='/-/book/{{ book.hash_id }}/sale/{{ sale.id }}'>撤销</a>
                    {% else %}
                        --
                    {% endif %}
                </td>
            </tr>
        {% else %}
            <tr>
                <td class='text-center text-muted' colspan='5'>暂无记录</td>
            </tr>
        {% endfor %}
        </tbody>
    </table>
{% endblock %}
{% block scripts %}
    <script type='text/javascript'>
        window.import("book/sale");
    </script>
{% endblock %}
