{% extends "org/layout.twig" %}
{% block web_title %}
    收入管理
{% endblock %}
{% block page_title %}
    收入管理
{% endblock %}
{% block page_body %}
    {# 账户与余额信息卡片 #}
    {% embed "components/card.twig" %}
        {% block header %}
            <i class="bi bi-wallet2 me-2"></i>账户与余额信息
        {% endblock %}
        {% block body %}
            <div class="row">
                {# 结算账户信息 #}
                <div class="col-md-4">
                    <div class="d-flex align-items-center h-100">
                        <div class="me-3">
                            <img src="https://via.placeholder.com/50x50/007bff/ffffff?text=U"
                                 alt="创始人头像"
                                 class="rounded-circle"
                                 width="50"
                                 height="50">
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-1">创始人用户名</h6>
                            <small class="text-muted">企业创始人</small>
                            <div class="mt-1">
                                <span class="badge bg-success-subtle text-success">已认证</span>
                            </div>
                        </div>
                    </div>
                </div>

                {# 当前余额 #}
                <div class="col-md-4">
                    <div class="text-center p-3 bg-light rounded h-100 d-flex flex-column justify-content-center">
                        <h3 class="text-primary mb-1">￥1,234.56</h3>
                        <p class="text-muted mb-0 small">当前余额</p>
                        <small class="text-muted">最近7天收入</small>
                    </div>
                </div>

                {# 结算信息与操作 #}
                <div class="col-md-4">
                    <div class="h-100 d-flex flex-column justify-content-center">
                        <div class="mb-2">
                            <i class="bi bi-clock text-warning me-2"></i>
                            <small><strong>结算周期：</strong>T+7</small>
                        </div>
                        <div class="mb-2">
                            <i class="bi bi-calendar-check text-info me-2"></i>
                            <small><strong>下次结算：</strong>2024-06-29</small>
                        </div>
                        <div class="mb-2">
                            <small class="text-muted">系统将自动结算到银行账户</small>
                        </div>
                        <div class="d-grid">
                            <button type="button" class="btn btn-outline-primary btn-sm" disabled>
                                <i class="bi bi-bank me-1"></i>提现到银行卡
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        {% endblock %}
    {% endembed %}

    {# 收入统计卡片 #}
    {% embed "components/card.twig" %}
        {% block header %}
            <i class="bi bi-graph-up me-2"></i>收入统计
        {% endblock %}
        {% block body %}
            <div class="row text-center">
                <div class="col-md-3">
                    <div class="border-end">
                        <h5 class="text-success mb-1">￥8,765.43</h5>
                        <small class="text-muted">本月收入</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="border-end">
                        <h5 class="text-info mb-1">￥25,432.10</h5>
                        <small class="text-muted">累计收入</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="border-end">
                        <h5 class="text-warning mb-1">￥23,197.54</h5>
                        <small class="text-muted">已结算</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div>
                        <h5 class="text-primary mb-1">156</h5>
                        <small class="text-muted">付费用户</small>
                    </div>
                </div>
            </div>
        {% endblock %}
    {% endembed %}

    {# 收入明细卡片 #}
    {% embed "components/card.twig" %}
        {% block header %}
            <i class="bi bi-list-ul me-2"></i>收入明细
        {% endblock %}
        {% block body %}
            <table class="table table-hover align-middle mb-0">
                <thead class="table-light">
                <tr>
                    <th width='150'>时间</th>
                    <th>描述</th>
                    <th width='120'>来源</th>
                    <th width='100' class="text-end">金额</th>
                </tr>
                </thead>
                <tbody>
                {% for earnings in earningses %}
                    <tr>
                        <td>
                            <small class="text-muted">{{ earnings.create_time }}</small>
                        </td>
                        <td>{{ earnings.info }}</td>
                        <td>
                            {% if earnings.type == 1 %}
                                <span class="badge bg-success-subtle text-success">收入</span>
                            {% else %}
                                <span class="badge bg-warning-subtle text-warning">结算</span>
                            {% endif %}
                        </td>
                        <td class="text-end">
                            {% if earnings.type == 1 %}
                                <span class='text-success fw-bold'>+ ￥{{ format_price(earnings.amount) }}</span>
                            {% else %}
                                <span class='text-danger fw-bold'>- ￥{{ format_price(earnings.amount) }}</span>
                            {% endif %}
                        </td>
                    </tr>
                {% else %}
                    <tr>
                        <td colspan='4' class='text-muted text-center py-4'>
                            <i class="bi bi-inbox display-6 text-muted d-block mb-2"></i>
                            暂无收入记录
                        </td>
                    </tr>
                {% endfor %}
                </tbody>
            </table>
        {% endblock %}
    {% endembed %}
{% endblock %}

{% block scripts %}
    <script type='text/javascript'>

    </script>
{% endblock %}
